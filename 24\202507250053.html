<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Vibe Music Player</title>
  <style>
    :root {
      --bg-950: #0b1120; /* slate-950 */
      --bg-900: #0f172a; /* slate-900 */
      --bg-800: #1f2937; /* gray-800 */
      --bg-700: #334155; /* slate-700 */
      --text-100: #e5e7eb; /* gray-200 */
      --text-300: #cbd5e1; /* slate-300 */
      --text-500: #94a3b8; /* slate-400 */
      --accent-500: #6366f1; /* indigo-500 */
      --accent-600: #4f46e5; /* indigo-600 */
      --accent-700: #4338ca; /* indigo-700 */
      --rose-500: #f43f5d;
      --emerald-500: #10b981;
      --sky-500: #0ea5e9;
      --yellow-400: #facc15;
      --radius-lg: 18px;
      --radius-md: 12px;
      --radius-sm: 10px;
      --shadow-xl: 0 20px 60px rgba(0,0,0,0.35);
      --shadow-md: 0 10px 30px rgba(0,0,0,0.25);
      --shadow-sm: 0 6px 18px rgba(0,0,0,0.18);
    }
    * { box-sizing: border-box; }
    html, body { height: 100%; }
    body {
      margin: 0;
      font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, Noto Sans, "Apple Color Emoji", "Segoe UI Emoji";
      color: var(--text-100);
      background: radial-gradient(circle at 20% 20%, #1e1b4b, var(--bg-950) 50%), linear-gradient(160deg, #0f172a, #111827);
      overflow: hidden;
    }

    /* Canvas background */
    #viz {
      position: fixed;
      inset: 0;
      width: 100vw; height: 100vh;
      display: block;
      z-index: 0;
    }

    .gradient-overlay {
      position: fixed; inset: 0;
      background: radial-gradient(800px 800px at 70% 20%, rgba(99,102,241,0.12), transparent 70%),
                  radial-gradient(900px 900px at 10% 80%, rgba(244,63,94,0.08), transparent 70%);
      pointer-events: none; z-index: 1;
    }

    .app {
      position: relative; z-index: 2;
      height: 100%; width: 100%;
      display: grid; place-items: center;
      padding: 2rem;
    }

    .player {
      width: 100%; max-width: 1040px;
      display: grid; grid-template-columns: 360px 1fr; gap: 2rem;
      background: linear-gradient(180deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02));
      border: 1px solid rgba(255,255,255,0.08);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-xl);
      backdrop-filter: blur(10px) saturate(120%);
      -webkit-backdrop-filter: blur(10px) saturate(120%);
      padding: 2rem;
    }

    @media (max-width: 1024px) {
      .player { grid-template-columns: 1fr; }
    }

    header.bar {
      grid-column: 1 / -1;
      display: flex; align-items: center; justify-content: space-between;
      margin-bottom: 0.5rem;
    }
    .brand {
      display: inline-flex; align-items: center; gap: 0.75rem;
      font-weight: 700; letter-spacing: -0.02em; font-size: 1.125rem;
    }
    .brand-icon {
      width: 40px; height: 40px; border-radius: 12px;
      display: grid; place-items: center;
      background: linear-gradient(135deg, var(--accent-600), var(--rose-500));
      box-shadow: var(--shadow-sm);
    }

    .left {
      display: grid; gap: 1.25rem;
    }

    .cover-wrap {
      position: relative; aspect-ratio: 1 / 1; width: 100%; border-radius: var(--radius-md);
      overflow: hidden; box-shadow: var(--shadow-md);
      background: linear-gradient(145deg, #1f2937, #111827);
      border: 1px solid rgba(255,255,255,0.06);
    }

    .cover {
      position: absolute; inset: 0; display: grid; place-items: center;
      background: radial-gradient(circle at 50% 50%, rgba(99,102,241,0.15), transparent 60%);
    }
    .vinyl {
      width: 68%; height: 68%; border-radius: 9999px;
      background: conic-gradient(from 0deg, #0f172a, #111827 25%, #0f172a 50%, #111827 75%, #0f172a 100%);
      border: 6px solid #0b1120; box-shadow: inset 0 0 0 2px rgba(255,255,255,0.08), 0 8px 24px rgba(0,0,0,0.35);
      animation: spin 18s linear infinite;
    }
    .vinyl::after {
      content: ""; position: absolute; inset: 0; margin: auto; width: 24%; height: 24%; border-radius: 9999px;
      background: radial-gradient(circle at 30% 30%, var(--accent-500), var(--accent-700));
      box-shadow: 0 0 30px rgba(99,102,241,0.5);
    }
    .vinyl.paused { animation-play-state: paused; }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .file-zone {
      border: 2px dashed rgba(255,255,255,0.15);
      border-radius: var(--radius-md);
      padding: 1rem; text-align: center;
      color: var(--text-500);
      transition: border-color .2s, background-color .2s;
      cursor: pointer; user-select: none;
    }
    .file-zone.dragover {
      border-color: var(--accent-500);
      background: rgba(99,102,241,0.08);
      color: var(--text-300);
    }

    .right {
      display: grid; grid-template-rows: auto auto 1fr auto; gap: 1.25rem;
    }

    .title {
      font-size: 1.5rem; font-weight: 700; letter-spacing: -0.02em;
    }
    .subtitle { color: var(--text-500); font-size: 0.95rem; }

    .controls {
      display: grid; gap: 1rem;
      background: rgba(255,255,255,0.04);
      border: 1px solid rgba(255,255,255,0.06);
      border-radius: var(--radius-md);
      padding: 1rem;
    }

    .time-row {
      display: grid; gap: 0.5rem;
    }
    .timebar {
      display: flex; align-items: center; gap: 0.75rem;
    }
    .timebar .time { font-feature-settings: "tnum" on, "lnum" on; color: var(--text-500); font-size: 0.9rem; min-width: 52px; text-align: center; }

  input[type="range"] {
    -webkit-appearance: none; appearance: none; width: 100%; height: 12px;  background: transparent;
    padding: 8px 0;
    margin: -8px 0;
  }
  input[type="range"]:focus { outline: none; }
  input[type="range"]::-webkit-slider-runnable-track {
    width: 100%; height: 12px; background: linear-gradient(90deg, var(--accent-500) var(--progress, 0%), rgba(255,255,255,0.18) var(--progress, 0%));
    border-radius: 999px;
  }
  input[type="range"]::-moz-range-track {
    width: 100%; height: 12px; /* 轨道加高 */ background: linear-gradient(90deg, var(--accent-500) var(--progress, 0%), rgba(255,255,255,0.18) var(--progress, 0%));
    border-radius: 999px;
  }
  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none; appearance: none; width: 18px; height: 18px; /* 手柄也可稍微增大 */ border-radius: 50%;
    background: #fff; box-shadow: 0 4px 10px rgba(0,0,0,0.25);
    margin-top: -3px; /* 调整垂直居中位置 (12px轨道 - 18px手柄)/2 */ border: 3px solid var(--accent-600);
    cursor: pointer;
  }
    input[type="range"]::-moz-range-thumb {
      width: 18px; height: 18px; border-radius: 50%; background: #fff; border: 3px solid var(--accent-600); cursor: pointer;
      box-shadow: 0 4px 10px rgba(0,0,0,0.25);
    }

    .media-buttons {
      display: flex; align-items: center; gap: 0.5rem; flex-wrap: wrap;
    }
    .btn {
      display: inline-flex; align-items: center; justify-content: center; gap: 0.5rem;
      padding: 0.6rem 0.9rem; border-radius: var(--radius-sm);
      border: 1px solid rgba(255,255,255,0.10);
      background: linear-gradient(180deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05));
      color: var(--text-100); cursor: pointer; user-select: none;
      transition: transform .12s ease, box-shadow .2s ease, background-color .2s ease;
    }
    .btn:hover { transform: translateY(-1px); box-shadow: var(--shadow-sm); }
    .btn:active { transform: translateY(0); }
    .btn.primary {
      background: linear-gradient(135deg, var(--accent-600), var(--rose-500));
      border-color: transparent;
    }
    .btn.muted { background: linear-gradient(180deg, rgba(255,255,255,0.08), rgba(255,255,255,0.02)); }
    .btn.toggle.active { outline: 2px solid var(--accent-500); box-shadow: 0 0 0 2px rgba(99,102,241,0.2); }

    .mixers {
      display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem;
    }
    @media (max-width: 520px) {
      .mixers { grid-template-columns: 1fr; }
      .media-buttons { justify-content: space-between; }
    }
    .mixer {
      background: rgba(255,255,255,0.03);
      border: 1px solid rgba(255,255,255,0.06);
      border-radius: var(--radius-sm);
      padding: 0.75rem; display: grid; gap: 0.6rem;
    }
    .mixer h4 { margin: 0; font-size: 0.95rem; font-weight: 600; color: var(--text-300); }
    .mini-row { display: flex; align-items: center; gap: 0.75rem; }
    .mini-row label { color: var(--text-500); font-size: 0.85rem; }

    .effects {
      background: rgba(255,255,255,0.03);
      border: 1px solid rgba(255,255,255,0.06);
      border-radius: var(--radius-md);
      padding: 1rem; display: grid; gap: 1rem;
    }

    .toggle-row { display: flex; flex-wrap: wrap; gap: 0.5rem; }

    .footer {
      grid-column: 1 / -1; text-align: center; color: var(--text-500); font-size: 0.9rem; margin-top: 0.5rem;
    }

    .sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0; }
  </style>
</head>
<body>
  <canvas id="viz"></canvas>
  <div class="gradient-overlay" aria-hidden="true"></div>
  <div class="app">
    <div class="player">
      <header class="bar">
        <div class="brand" aria-label="Vibe Player">
          <div class="brand-icon" aria-hidden="true">
            <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 3C7.03 3 3 7.03 3 12s4.03 9 9 9 9-4.03 9-9S16.97 3 12 3Zm0 14.5A5.5 5.5 0 1 1 12 6.5a5.5 5.5 0 0 1 0 11Z" fill="white"/>
              <path d="M12 10.25a1.75 1.75 0 1 0 0 3.5 1.75 1.75 0 0 0 0-3.5Z" fill="#0ea5e9"/>
            </svg>
          </div>
          <span>Vibe Player</span>
        </div>
        <div style="display:flex;gap:0.5rem;align-items:center;">
          <button id="demoBtn" class="btn">Load demo</button>
          <a href="#" id="downloadLink" class="btn muted" download style="text-decoration:none;">Download current</a>
        </div>
      </header>

      <div class="left">
        <div class="cover-wrap">
          <div class="cover">
            <div id="vinyl" class="vinyl paused" aria-hidden="true"></div>
          </div>
        </div>
        <label for="file" class="file-zone" id="dropzone">
          <input id="file" type="file" accept="audio/*" class="sr-only" />
          <strong>Click to select</strong> or drag & drop an audio file here
        </label>
      </div>

      <div class="right">
        <div>
          <div class="title" id="trackTitle">No track loaded</div>
          <div class="subtitle" id="trackMeta">Load a file or paste a URL to begin</div>
        </div>

        <div class="controls">
          <div class="time-row">
            <div class="timebar">
              <span class="time" id="current">0:00</span>
              <input type="range" id="seek" min="0" max="100" value="0" step="0.01" aria-label="Seek" />
              <span class="time" id="duration">0:00</span>
            </div>
          </div>

          <div class="media-buttons">
            <button id="back10" class="btn" title="Back 10s" aria-label="Back 10 seconds">⏪ 10s</button>
            <button id="play" class="btn primary" aria-label="Play/Pause">▶️ Play</button>
            <button id="forward10" class="btn" title="Forward 10s" aria-label="Forward 10 seconds">10s ⏩</button>
            <button id="stop" class="btn" aria-label="Stop">⏹️ Stop</button>
            <button id="mute" class="btn toggle" aria-label="Mute">🔇 Mute</button>
          </div>

          <div class="mixers">
            <div class="mixer">
              <h4>Volume</h4>
              <div class="mini-row">
                <span aria-hidden="true">🔊</span>
                <input type="range" id="volume" min="0" max="1" step="0.01" value="0.9" aria-label="Volume" />
                <span id="volVal" style="min-width:3ch;text-align:right;">90</span>
              </div>
            </div>
            <div class="mixer">
              <h4>Speed</h4>
              <div class="mini-row" style="width:100%">
                <input type="range" id="speed" min="0.5" max="2" step="0.01" value="1" aria-label="Playback speed" style="width:100%" />
                <span id="speedVal" style="min-width:4.2ch;text-align:right;">1.00×</span>
              </div>
              <div class="mini-row" style="justify-content:space-between;">
                <button class="btn muted" data-speed="0.75">0.75×</button>
                <button class="btn muted" data-speed="1">1×</button>
                <button class="btn muted" data-speed="1.25">1.25×</button>
                <button class="btn muted" data-speed="1.5">1.5×</button>
              </div>
            </div>
            <div class="mixer">
              <h4>Echo</h4>
              <label class="mini-row"><span style="width:60px;">Mix</span><input id="echoMix" type="range" min="0" max="1" step="0.01" value="0.0" /><span id="echoMixVal" style="min-width:3ch;text-align:right;">0</span></label>
              <label class="mini-row"><span style="width:60px;">Time</span><input id="echoTime" type="range" min="0" max="1.2" step="0.01" value="0.35" /><span id="echoTimeVal" style="min-width:4ch;text-align:right;">0.35s</span></label>
              <label class="mini-row"><span style="width:60px;">Feedback</span><input id="echoFb" type="range" min="0" max="0.9" step="0.01" value="0.3" /><span id="echoFbVal" style="min-width:4ch;text-align:right;">0.30</span></label>
            </div>
          </div>
        </div>

        <div class="effects">
          <div style="display:flex;align-items:center;justify-content:space-between;gap:1rem;flex-wrap:wrap;">
            <h3 style="margin:0;font-size:1.05rem;">Visuals & Tone</h3>
            <div style="display:flex;gap:0.5rem;align-items:center;">
              <input type="text" id="urlInput" placeholder="Paste audio URL" style="width:280px;max-width:50vw;padding:0.6rem 0.8rem;border-radius:10px;border:1px solid rgba(255,255,255,0.08);background:rgba(255,255,255,0.04);color:var(--text-100);" />
              <button id="loadUrl" class="btn">Load</button>
            </div>
          </div>

          <div class="toggle-row">
            <button id="tBars" class="btn toggle active" aria-pressed="true">Equalizer Bars</button>
            <button id="tParticles" class="btn toggle active" aria-pressed="true">Particles</button>
            <button id="tGlow" class="btn toggle active" aria-pressed="true">Glow Pulse</button>
          </div>

          <div class="mixers" style="grid-template-columns: repeat(2, 1fr);">
            <div class="mixer">
              <h4>Bass Boost</h4>
              <label class="mini-row"><span style="width:60px;">Gain</span><input id="bassGain" type="range" min="-10" max="20" step="0.1" value="0" /><span id="bassGainVal" style="min-width:4ch;text-align:right;">0 dB</span></label>
            </div>
            <div class="mixer">
              <h4>Colors</h4>
              <div class="mini-row">
                <label style="display:flex;gap:0.5rem;align-items:center;">
                  <input id="colorAuto" type="checkbox" checked /> Auto hues
                </label>
              </div>
              <div class="mini-row">
                <label style="display:flex;gap:0.5rem;align-items:center;">
                  <span style="width:60px;">Tint</span>
                  <input id="colorPicker" type="color" value="#6366f1" />
                </label>
              </div>
            </div>
          </div>
        </div>

        <div class="footer">
          Space = Play/Pause • ← / → = Seek • ↑ / ↓ = Volume • Made with Web Audio API & Canvas
        </div>
      </div>

      <audio id="audio" crossorigin="anonymous"></audio>
    </div>
  </div>

  <script>
    // Elements
    const audio = document.getElementById('audio');
    const playBtn = document.getElementById('play');
    const stopBtn = document.getElementById('stop');
    const back10Btn = document.getElementById('back10');
    const forward10Btn = document.getElementById('forward10');
    const muteBtn = document.getElementById('mute');
    const seek = document.getElementById('seek');
    const currentEl = document.getElementById('current');
    const durationEl = document.getElementById('duration');
    const volumeSlider = document.getElementById('volume');
    const volVal = document.getElementById('volVal');
    const speedSlider = document.getElementById('speed');
    const speedVal = document.getElementById('speedVal');
    const vinyl = document.getElementById('vinyl');
    const trackTitle = document.getElementById('trackTitle');
    const trackMeta = document.getElementById('trackMeta');
    const fileInput = document.getElementById('file');
    const dropzone = document.getElementById('dropzone');
    const urlInput = document.getElementById('urlInput');
    const loadUrlBtn = document.getElementById('loadUrl');
    const demoBtn = document.getElementById('demoBtn');
    const downloadLink = document.getElementById('downloadLink');

    // Visual toggles
    const tBars = document.getElementById('tBars');
    const tParticles = document.getElementById('tParticles');
    const tGlow = document.getElementById('tGlow');

    // Effect sliders
    const echoMix = document.getElementById('echoMix');
    const echoMixVal = document.getElementById('echoMixVal');
    const echoTime = document.getElementById('echoTime');
    const echoTimeVal = document.getElementById('echoTimeVal');
    const echoFb = document.getElementById('echoFb');
    const echoFbVal = document.getElementById('echoFbVal');
    const bassGain = document.getElementById('bassGain');
    const bassGainVal = document.getElementById('bassGainVal');

    // Color controls
    const colorAuto = document.getElementById('colorAuto');
    const colorPicker = document.getElementById('colorPicker');

    // Canvas
    const canvas = document.getElementById('viz');
    const ctx2d = canvas.getContext('2d');

    // Audio context graph
    let actx = null;
    let sourceNode = null;
    let analyser = null;
    let masterGain = null;
    let dryGain = null;
    let wetGain = null;
    let lowShelf = null;
    let delayNode = null;
    let feedback = null;
    let vizGain = null;
    let graphBuilt = false;

    // State
    let showBars = true, showParticles = true, showGlow = true;
    let hasUserInteracted = false;
    let currentObjectUrl = null;
    let dominantHue = 250; // default indigo-ish
    let particleHue = 340; // rose-ish

    // Particles
    const PARTICLE_COUNT = 90;
    let particles = [];

    function makeParticles() {
      particles = Array.from({length: PARTICLE_COUNT}, () => ({
        angle: Math.random() * Math.PI * 2,
        radius: 60 + Math.random() * 180,
        size: 2 + Math.random() * 3,
        speed: 0.002 + Math.random() * 0.004,
        life: Math.random(),
      }));
    }
    makeParticles();

    function buildAudioGraph() {
      if (graphBuilt) return;
      actx = new (window.AudioContext || window.webkitAudioContext)();

      sourceNode = actx.createMediaElementSource(audio);
      analyser = actx.createAnalyser();
      analyser.fftSize = 2048; // 1024 bins for frequency
      analyser.smoothingTimeConstant = 0.85;

      masterGain = actx.createGain();
      masterGain.gain.value = 1;

      dryGain = actx.createGain();
      wetGain = actx.createGain();
      wetGain.gain.value = parseFloat(echoMix.value);

      lowShelf = actx.createBiquadFilter();
      lowShelf.type = 'lowshelf';
      lowShelf.frequency.value = 200; // boost low frequencies
      lowShelf.gain.value = parseFloat(bassGain.value);

      delayNode = actx.createDelay(5.0);
      delayNode.delayTime.value = parseFloat(echoTime.value);
      feedback = actx.createGain();
      feedback.gain.value = parseFloat(echoFb.value);

      vizGain = actx.createGain();

      // Connections: source -> master
      sourceNode.connect(masterGain);
      // Dry path
      masterGain.connect(lowShelf); // optional bass boost on both paths
      lowShelf.connect(dryGain);
      dryGain.connect(actx.destination);

      // Wet path (echo)
      lowShelf.connect(wetGain);
      wetGain.connect(delayNode);
      delayNode.connect(feedback);
      feedback.connect(delayNode);
      delayNode.connect(actx.destination);

      // Visual tap (post lowShelf, pre destination merging)
      lowShelf.connect(vizGain);
      vizGain.connect(analyser);

      graphBuilt = true;
    }

    function resumeCtx() {
      if (!graphBuilt) buildAudioGraph();
      if (actx && actx.state === 'suspended') actx.resume();
    }

    function formatTime(sec) {
      if (!isFinite(sec)) return '0:00';
      const s = Math.floor(sec % 60).toString().padStart(2, '0');
      const m = Math.floor(sec / 60);
      return `${m}:${s}`;
    }

    function setRangeProgress(el, value, max = 1) {
      const pct = Math.max(0, Math.min(1, value / max)) * 100;
      el.style.setProperty('--progress', pct + '%');
    }

    function updateSeek() {
      const cur = audio.currentTime || 0;
      const dur = audio.duration || 0;
      currentEl.textContent = formatTime(cur);
      durationEl.textContent = isFinite(dur) ? formatTime(dur) : '0:00';
      seek.max = dur || 100;
      seek.value = cur;
      setRangeProgress(seek, cur, dur || 100);
    }

    function updateButtons() {
      if (audio.paused) {
        playBtn.textContent = '▶️ Play';
        vinyl.classList.add('paused');
      } else {
        playBtn.textContent = '⏸️ Pause';
        vinyl.classList.remove('paused');
      }
      muteBtn.classList.toggle('active', audio.muted);
      muteBtn.setAttribute('aria-pressed', audio.muted ? 'true' : 'false');
    }

    function loadFile(file) {
      if (!file) return;
      const url = URL.createObjectURL(file);
      setSource(url, file.name);
      currentObjectUrl = url; // track to revoke later
      downloadLink.href = url;
      downloadLink.download = file.name || 'track';
    }

    async function loadUrl(src) {
      if (!src) return;
      try {
        const res = await fetch(src);
        const blob = await res.blob();
        if (!blob.type.startsWith('audio/')) throw new Error('Not an audio file');
        if (currentObjectUrl) URL.revokeObjectURL(currentObjectUrl);
        const url = URL.createObjectURL(blob);
        setSource(url, src.split('/').pop() || 'stream');
        currentObjectUrl = url;
        downloadLink.href = url;
        downloadLink.download = 'downloaded-' + (src.split('/').pop() || 'track');
      } catch (e) {
        alert('Failed to load URL: ' + e.message);
      }
    }

    function setSource(url, name = 'Unknown') {
      audio.src = url;
      audio.load();
      trackTitle.textContent = name;
      trackMeta.textContent = 'Ready • ' + new Date().toLocaleString();
      hasUserInteracted = true;
      resumeCtx();
      audio.play().catch(() => {});
      updateButtons();
    }

    function attachEvents() {
      playBtn.addEventListener('click', async () => {
        resumeCtx();
        if (audio.paused) {
          await audio.play().catch(()=>{});
        } else {
          audio.pause();
        }
        updateButtons();
      });

      stopBtn.addEventListener('click', () => {
        audio.pause(); audio.currentTime = 0; updateSeek(); updateButtons();
      });

      back10Btn.addEventListener('click', () => { audio.currentTime = Math.max(0, audio.currentTime - 10); });
      forward10Btn.addEventListener('click', () => { audio.currentTime = Math.min(audio.duration || Infinity, audio.currentTime + 10); });

      muteBtn.addEventListener('click', () => { audio.muted = !audio.muted; updateButtons(); });

      volumeSlider.addEventListener('input', () => {
        audio.volume = parseFloat(volumeSlider.value);
        volVal.textContent = Math.round(audio.volume * 100);
        setRangeProgress(volumeSlider, audio.volume, 1);
      });
      setRangeProgress(volumeSlider, volumeSlider.value, 1);
      volVal.textContent = Math.round(parseFloat(volumeSlider.value) * 100);

      speedSlider.addEventListener('input', () => {
        audio.playbackRate = parseFloat(speedSlider.value);
        speedVal.textContent = audio.playbackRate.toFixed(2) + '×';
        setRangeProgress(speedSlider, audio.playbackRate - 0.5, 1.5);
      });
      speedVal.textContent = '1.00×';

      document.querySelectorAll('[data-speed]').forEach(btn => {
        btn.addEventListener('click', () => {
          const v = parseFloat(btn.getAttribute('data-speed'));
          speedSlider.value = String(v);
          speedSlider.dispatchEvent(new Event('input'));
        });
      });

      seek.addEventListener('input', () => {
        audio.currentTime = parseFloat(seek.value);
        updateSeek();
      });

      audio.addEventListener('timeupdate', updateSeek);
      audio.addEventListener('durationchange', updateSeek);
      audio.addEventListener('play', updateButtons);
      audio.addEventListener('pause', updateButtons);
      audio.addEventListener('ended', updateButtons);

      fileInput.addEventListener('change', () => {
        if (fileInput.files && fileInput.files[0]) loadFile(fileInput.files[0]);
      });

      // Drag and drop
      ['dragenter','dragover'].forEach(evt => dropzone.addEventListener(evt, e => {
        e.preventDefault(); e.stopPropagation(); dropzone.classList.add('dragover');
      }));
      ;['dragleave','drop'].forEach(evt => dropzone.addEventListener(evt, e => {
        e.preventDefault(); e.stopPropagation(); dropzone.classList.remove('dragover');
      }));
      dropzone.addEventListener('drop', e => {
        const files = e.dataTransfer.files; if (files && files[0]) loadFile(files[0]);
      });

      loadUrlBtn.addEventListener('click', () => {
        const v = urlInput.value.trim(); if (v) loadUrl(v);
      });
      urlInput.addEventListener('keydown', e => {
        if (e.key === 'Enter') { e.preventDefault(); loadUrlBtn.click(); }
      });

      demoBtn.addEventListener('click', () => {
        // A tiny built-in beep sample (1s sine tone, 440Hz), WAV base64
        const demoSrc = "data:audio/wav;base64,UklGRmQAAABXQVZFZm10IBAAAAABAAEAESsAACJWAAACABYAZGF0YUEAAACQAAAAkAAAAP8AAP8A/wD/AAAA/wAAAP8AAP8A/wD/AAAA/wAAAP8A/wD/AP8A/wD/AP8A/wD/AAAA/wAAAP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wAAAP8AAP8A/wD/AAAA/wAAAP8AAP8A/wD/AAAA/wAAAP8AAP8A/wD/AAAA/wAAAP8AAP8A/wD/AAAA/wAAAP8AAP8A/wD/AAAA/wAAAP8AAP8A/wD/AAAA/wAAAP8AAP8A/wD/AAAA/wAAAP8AAP8A/wD/AAAA/wAAAP8AAP8A/wD/AAAA/wAAAP8A";
        setSource(demoSrc, 'Demo sine 440Hz');
        downloadLink.href = demoSrc;
        downloadLink.download = 'demo-440hz.wav';
      });

      // Visual toggles
      function toggleBtn(btn, stateVarName) {
        btn.addEventListener('click', () => {
          const val = !(btn.classList.contains('active'));
          btn.classList.toggle('active', val);
          btn.setAttribute('aria-pressed', val ? 'true' : 'false');
          if (stateVarName === 'showBars') showBars = val;
          if (stateVarName === 'showParticles') showParticles = val;
          if (stateVarName === 'showGlow') showGlow = val;
        });
      }
      toggleBtn(tBars, 'showBars');
      toggleBtn(tParticles, 'showParticles');
      toggleBtn(tGlow, 'showGlow');

      // Effects controls
      echoMix.addEventListener('input', () => {
        echoMixVal.textContent = Math.round(echoMix.value * 100);
        if (wetGain) wetGain.gain.value = parseFloat(echoMix.value);
        setRangeProgress(echoMix, echoMix.value, 1);
      });
      echoTime.addEventListener('input', () => {
        echoTimeVal.textContent = parseFloat(echoTime.value).toFixed(2) + 's';
        if (delayNode) delayNode.delayTime.value = parseFloat(echoTime.value);
        setRangeProgress(echoTime, echoTime.value, 1.2);
      });
      echoFb.addEventListener('input', () => {
        echoFbVal.textContent = parseFloat(echoFb.value).toFixed(2);
        if (feedback) feedback.gain.value = parseFloat(echoFb.value);
        setRangeProgress(echoFb, echoFb.value, 0.9);
      });
      bassGain.addEventListener('input', () => {
        const val = parseFloat(bassGain.value);
        bassGainVal.textContent = val.toFixed(1) + ' dB';
        if (lowShelf) lowShelf.gain.value = val;
        setRangeProgress(bassGain, val + 10, 30); // -10..20 mapped to 0..30
      });
      // Initialize progress bars for effect sliders
      echoMix.dispatchEvent(new Event('input'));
      echoTime.dispatchEvent(new Event('input'));
      echoFb.dispatchEvent(new Event('input'));
      bassGain.dispatchEvent(new Event('input'));

      // Keyboard shortcuts
      window.addEventListener('keydown', (e) => {
        if (document.activeElement && (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA')) return;
        if (e.code === 'Space') { e.preventDefault(); playBtn.click(); }
        if (e.code === 'ArrowLeft') { e.preventDefault(); back10Btn.click(); }
        if (e.code === 'ArrowRight') { e.preventDefault(); forward10Btn.click(); }
        if (e.code === 'ArrowUp') { e.preventDefault(); volumeSlider.value = Math.min(1, parseFloat(volumeSlider.value)+0.05); volumeSlider.dispatchEvent(new Event('input')); }
        if (e.code === 'ArrowDown') { e.preventDefault(); volumeSlider.value = Math.max(0, parseFloat(volumeSlider.value)-0.05); volumeSlider.dispatchEvent(new Event('input')); }
      });

      window.addEventListener('resize', resizeCanvas);
      resizeCanvas();
      requestAnimationFrame(draw);
    }

    function resizeCanvas() {
      const dpr = Math.min(window.devicePixelRatio || 1, 2);
      const w = window.innerWidth;
      const h = window.innerHeight;
      canvas.width = Math.floor(w * dpr);
      canvas.height = Math.floor(h * dpr);
      canvas.style.width = w + 'px';
      canvas.style.height = h + 'px';
      ctx2d.setTransform(dpr, 0, 0, dpr, 0, 0);
    }

    const freqData = new Uint8Array(1024);
    const timeData = new Uint8Array(2048);
    let t = 0; // time

    function lerp(a,b,x){ return a + (b-a) * x; }

    function draw() {
      const w = canvas.clientWidth, h = canvas.clientHeight;
      ctx2d.clearRect(0,0,w,h);

      let low = 0, mid = 0, high = 0, amp = 0;
      if (analyser) {
        analyser.getByteFrequencyData(freqData);
        analyser.getByteTimeDomainData(timeData);
        const n = freqData.length;
        const lCount = Math.floor(n * 0.1);
        const mCount = Math.floor(n * 0.3);
        for (let i=0;i<lCount;i++) low += freqData[i];
        for (let i=lCount;i<lCount+mCount;i++) mid += freqData[i];
        for (let i=lCount+mCount;i<n;i++) high += freqData[i];
        low /= lCount || 1; mid /= mCount || 1; high /= (n-lCount-mCount) || 1;
        // amplitude from waveform
        let sum = 0;
        for (let i=0;i<timeData.length;i++) {
          const v = timeData[i] / 128 - 1; // -1..1
          sum += v*v;
        }
        amp = Math.sqrt(sum / timeData.length); // RMS 0..~1
      } else {
        // idle animation
        t += 0.01;
        low = 40 + Math.sin(t*1.3) * 20;
        mid = 30 + Math.sin(t*1.7+1) * 20;
        high = 20 + Math.sin(t*2.1+2) * 20;
        amp = (Math.sin(t*2)+1)/8;
      }

      // Auto hue from spectrum if enabled
      if (colorAuto.checked) {
        // Map frequency bands to color channels / hue blending
        const h1 = 260; // indigo
        const h2 = 195; // sky
        const h3 = 330; // pink/rose
        const tot = low + mid + high + 1e-6;
        dominantHue = (h1 * (low/tot) + h2 * (mid/tot) + h3 * (high/tot));
        particleHue = (dominantHue + 70) % 360;
      } else {
        const c = colorPicker.value; // hex to hue quickly
        const hue = hexToHue(c);
        dominantHue = hue;
        particleHue = (hue + 70) % 360;
      }

      const centerX = w/2, centerY = h/2;

      // Background gradient
      const bgGrad = ctx2d.createRadialGradient(centerX, centerY, 0, centerX, centerY, Math.max(w,h)*0.8);
      bgGrad.addColorStop(0, `hsla(${dominantHue}, 82%, 58%, ${0.12 + amp*0.2})`);
      bgGrad.addColorStop(1, 'rgba(0,0,0,0)');
      ctx2d.fillStyle = bgGrad;
      ctx2d.fillRect(0,0,w,h);

      // Glow pulse circle
      if (showGlow) {
        const radius = lerp(Math.min(w,h)*0.10, Math.min(w,h)*0.28, Math.min(1, amp*2 + low/255 * 0.4));
        ctx2d.globalCompositeOperation = 'lighter';
        for (let i=0;i<5;i++) {
          ctx2d.beginPath();
          ctx2d.arc(centerX, centerY, radius + i*18, 0, Math.PI*2);
          const alpha = 0.12 - i*0.02;
          ctx2d.strokeStyle = `hsla(${dominantHue}, 82%, ${60 - i*6}%, ${Math.max(alpha,0)})`;
          ctx2d.lineWidth = 6 - i;
          ctx2d.stroke();
        }
        ctx2d.globalCompositeOperation = 'source-over';
      }

      // Equalizer bars
      if (showBars) {
        const bars = 96;
        const step = Math.floor(freqData.length / bars);
        const barW = Math.max(2, w / bars);
        for (let i=0;i<bars;i++) {
          const v = freqData[i*step] || 0;
          const barH = (v/255) * (h*0.35);
          const x = i * barW;
          const y = h - barH - 20;
          const hue = (dominantHue + (i/bars)*60) % 360;
          ctx2d.fillStyle = `hsla(${hue}, 80%, 60%, 0.8)`;
          ctx2d.fillRect(x, y, barW*0.8, barH);
        }
      }

      // Particles
      if (showParticles) {
        for (let p of particles) {
          p.angle += p.speed + high/2550;
          const radFactor = 1 + amp*0.8 + high/255*0.6;
          const r = p.radius * radFactor;
          const x = centerX + Math.cos(p.angle) * r;
          const y = centerY + Math.sin(p.angle) * r * 0.6; // elliptical for depth
          const s = p.size + (mid/255) * 2.5;
          ctx2d.beginPath();
          const hue = particleHue;
          ctx2d.fillStyle = `hsla(${hue}, 90%, 64%, ${0.55 + amp*0.3})`;
          ctx2d.arc(x, y, s, 0, Math.PI*2);
          ctx2d.fill();
        }
      }

      requestAnimationFrame(draw);
    }

    function hexToHue(hex) {
      // Convert hex to HSL and return hue
      const c = hex.replace('#','');
      const r = parseInt(c.substring(0,2),16)/255;
      const g = parseInt(c.substring(2,4),16)/255;
      const b = parseInt(c.substring(4,6),16)/255;
      const max = Math.max(r,g,b), min = Math.min(r,g,b);
      let h = 0; const d = max - min;
      if (d === 0) h = 0; else if (max === r) h = ((g-b)/d) % 6; else if (max === g) h = (b-r)/d + 2; else h = (r-g)/d + 4;
      h = Math.round(h*60); if (h < 0) h += 360; return h;
    }

    attachEvents();
  </script>
</body>
</html>